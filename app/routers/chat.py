from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from typing import List
import logging

from app.database import get_db
from app.schemas import MessageIn, MessageOut
from app import crud

logger = logging.getLogger("chat")
router = APIRouter(prefix="/chat", tags=["chat"])


@router.post("/post_message", response_model=MessageOut)
async def post_message(msg: MessageIn, db: AsyncSession = Depends(get_db)):
    if not msg.username or not msg.text:
        raise HTTPException(status_code=400, detail="Username and text are required")
    try:
        message = await crud.create_message(db, msg.username, msg.text)
        return {"id": message.id, "username": msg.username, "text": message.text, "created_at": message.created_at}
    except Exception as e:
        logger.exception("Error creating message: %s", str(e))
        return JSONResponse(status_code=500, content={"error": str(e)})


@router.get("/get_messages", response_model=List[MessageOut])
@cache(expire=10)
async def get_messages(since: datetime = Query(...), db: AsyncSession = Depends(get_db)):
    if since is None:
        raise HTTPException(status_code=400, detail="Parameter 'since' is required!")
    try:
        messages = await crud.get_messages_since(db, since)
        return [{"id": msg.id, "username": msg.user.username, "text": msg.text,
                 "created_at": msg.created_at} for msg in messages]
    except Exception as e:
        logger.exception("Error retrieving messages: %s", str(e))
        return JSONResponse(status_code=500, content={"error": str(e)})


@router.get("/get_message_count")
async def count_messages(db: AsyncSession = Depends(get_db)):
    try:
        count = await crud.get_message_count(db)
        return {"total_messages": count}
    except Exception as e:
        logger.exception("Error counting messages: %s", str(e))
        return JSONResponse(status_code=500, content={"error": str(e)})