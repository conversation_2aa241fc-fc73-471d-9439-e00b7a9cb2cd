from fastapi import FastAP<PERSON>
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
import redis.asyncio as redis
from app.routers import chat
from app.logging_config import setup_logging

setup_logging()

app = FastAPI(title="Chat Service")
app.include_router(chat.router)


@app.on_event("startup")
async def startup():
    r = redis.Redis(host="chat-redis", port=6379, decode_responses=True)
    FastAPICache.init(RedisBackend(r), prefix="chat-cache")