from sqlalchemy import Sequence
from sqlalchemy.future import select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import selectinload
from app.models import User, Message
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime


async def get_or_create_user(db: AsyncSession, username: str) -> User:
    result = await db.execute(select(User).where(User.username == username))
    user = result.scalars().first()
    if not user:
        user = User(username=username)
        db.add(user)
        await db.commit()
        await db.refresh(user)
    return user


async def create_message(db: AsyncSession, username: str, text: str) -> Message:
    user = await get_or_create_user(db, username)
    message = Message(user_id=user.id, text=text)
    db.add(message)
    await db.commit()
    await db.refresh(message)
    return message


async def get_messages_since(db: AsyncSession, since: datetime) -> Sequence[Message]:
    result = await db.execute(
        select(Message)
        .options(selectinload(Message.user))  # подгружаем user
        .where(Message.created_at >= since)
        .order_by(Message.created_at)
    )
    return result.scalars().all()


async def get_message_count(db: AsyncSession) -> int:
    result = await db.execute(select(Message))
    return len(result.scalars().all())