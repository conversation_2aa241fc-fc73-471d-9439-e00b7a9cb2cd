# interview-task-vadim-bauer-shakhov

Task:

Create a Python Backend microservice that is for a global chat with a single room where anyone can:
1. Post message(username, text)
2. Get all messages since specific date
3. Total count of messages in chat since beginning of life of this global chat
4. Get notifications about new messages

Service must:
1. Use Python
2. Use FastAPI
3. REST API or reactive API or both, whatever you think needed in order to implement functionality above
4. Persist data in PostgreSQL or MySQL with schema migration support
5. Package manager of your choice(pip, Poetry, uv etc)
6. Have Swagger UI, so we can check API(at least for REST part)
7. Put information in readme about how to launch it using docker compose


This is a backend for a simple chat application built with:

-FastAPI – for the API
-SQLAlchemy + Alembic – for async ORM and DB migrations
-Pydantic – for data validation and schemas
-Redis – for small-scale caching
-Logging – to container logs (or stdout)

Minimal test coverage is provided.

Project Structure
📦 interview-task
├── 📁 app
│   ├── main.py               
│   ├── models.py             
│   ├── crud.py               
│   ├── schemas.py            
│   ├── database.py           
│   ├── logging_config.py     
│   └── 📁 routers
│       └── chat.py           
├── 📁 tests
│   └── test_chat.py          
├── alembic.ini               
├── 📁 alembic                
├── docker-compose.yml        
├── Dockerfile                
├── requirements.txt          
└── README.md                 

How to Run
git clone https://github.com/Influence360-HR/interview-task-vadim-bauer-shakhov
cd interview-task-vadim-bauer-shakhov

Initial database setup
Before running the project, you need to generate and apply initial Alembic migrations. You need Python 3.11 installed for this
Run the following commands in terminal only once (linux):
1. python3 -m venv venv
2. source venv/bin/activate
3. pip install -r requirements.txt
4. alembic init alembic    
5. now you need to check out alembic.ini in the project directory, and fix this string sqlalchemy.url = postgresql+psycopg2://chatuser:chatpass@db:5432/chatdb
6. alembic revision --autogenerate -m "Initial schema" 
7. Also might need to check file alembic/env.py
7. docker compose up --build

After the build is complete, the Swagger UI will be available at:
http://localhost:8000/docs

What Happens During docker compose up:
1.PostgreSQL container is launched
2.Alembic applies database migrations
3.Redis container is started
4.FastAPI app starts and serves the API

Important notes:
-Yes, secrets (DB creds) are currently stored in docker-compose.yml and alembic.ini.
-In a real project, I'd use .env, Docker secrets, or tools like Vault.
-Logs go to stdout (i.e. visible via docker logs). In production, you'd want to push them to something like Sentry.
-Validation errors and DB errors are handled separately.

API endpoints:
POST /chat/post_message	- Submit a new message
GET	 /chat/get_messages	- Get messages since a datetime
GET	 /chat/get_message_count -Total number of messages

About notifications:
Currently, polling is used for checking new messages (/chat/get_messages).
This is why caching via Redis is enabled for this endpoint.
Of course, in a real-time scenario, WebSockets would be a better fit.