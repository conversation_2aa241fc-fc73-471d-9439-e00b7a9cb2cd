import pytest
from httpx import AsyncClient, ASGITransport
from app.main import app
from datetime import datetime, timedelta

transport = ASGITransport(app=app)

@pytest.mark.asyncio
async def test_post_and_get_message():
    async with AsyncClient(transport=transport, base_url="http://localhost:8000") as client:
        post_response = await client.post("/chat/post_message", json={
            "username": "vadim",
            "text": "Hola bonita"
        },
        headers={
            "Content-Type": "application/json"
        })
        assert post_response.status_code == 200
        post_data = post_response.json()
        assert post_data["username"] == "vadim"
        assert post_data["text"] == "Hola bonita"
        assert "created_at" in post_data

        since = (datetime.utcnow() - timedelta(minutes=50)).isoformat()
        get_response = await client.get(f"/chat/get_messages?since={since}")
        assert get_response.status_code == 200
        data = get_response.json()
        assert len(data) >= 1

        count_response = await client.get("/chat/get_message_count")
        assert count_response.status_code == 200
        count_data = count_response.json()
        assert count_data["total_messages"] >= 1