services:
  db:
    image: postgres:15
    ports:
      - "5458:5432"
    environment:
      POSTGRES_USER: chatuser
      POSTGRES_PASSWORD: chatpass
      POSTGRES_DB: chatdb

  migrations:
    build: .
    depends_on:
      - db
    entrypoint: ["bash", "migrations.sh"]
    restart: "no"

  redis:
    image: redis:7
    container_name: chat-redis
    ports:
      - "6379:6379"

  web:
    build: .
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --workers 5
    depends_on:
      - db
      - migrations
      - redis
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql+asyncpg://chatuser:chatpass@db:5432/chatdb
      REDIS_HOST: redis
      REDIS_PORT: 6379